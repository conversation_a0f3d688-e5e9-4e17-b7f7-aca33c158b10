defmodule Admin.Integrations.RND.ExternalSFTP do
  @moduledoc """
  This module is responsible for interacting with the RND SFTP and
  managing files.

  """

  require Logger

  defp sftp_endpoint, do: Application.get_env(:admin, :rnd_sftp_endpoint)
  defp username, do: Application.get_env(:admin, :rnd_username)
  defp private_key_path, do: Application.get_env(:admin, :private_key_path)

  @doc """
  Uploads a file to RND for phone reassignment checks.

  Returns the name provided, expected that the next step is DNCSrub.poll, and later
  DNCScrub.download_and_parse
  Process:
  1. Save data to disk with name
  1. Upload file to SFTP under target, or _Upload (main project) by default
  1. Poll API to start scrub
  1. (optionally) wait for scrub to finish.
  """
  ### MARK: Upload RND File
  def upload_rnd_file(list_id, data) do
    # Clean up our temp files when we are done
    Temp.track()

    try do
      {:ok, temp_dir} = Temp.mkdir()
      {:ok, temp_file} = File.open(temp_dir <> "/" <> list_id, [:read, :write])
      :ok = IO.puts(temp_file, data)
      :ok = File.close(temp_file)
      {:ok, conn} = connect()
      {:ok, _server_path} = upload(conn, temp_dir, list_id)
      # :ok = poll_sftp()

      :ok
    rescue
      e ->
        Logger.log(:error, "Error uploading dnc scrub file")
        Logger.error(Exception.format(:error, e, __STACKTRACE__))
        {:error, e}
    end
  end

  def read(conn, list_id) do
    # Read result file to memory
    {:ok, results} = SFTPClient.read_file(conn, "/results/TNQueryUpload-#{list_id}.csv")

    # Parse result into object (abstracted out to the Rnd.Helpers module)
    nda_results =
      String.split(results, "\n")
      |> Enum.drop(2)
      |> Enum.drop(-1)
      |> Enum.map(fn record ->
        [phone_number, result, _, date | _] = String.split(record, ",")

        {phone_number, date, result}
      end)

    # Return object or message stating file doesn't exist.
    {:ok, nda_results}
  end

  def download(list_id) do
    {:ok, conn} = connect()
    read(conn, list_id)
  end

  def poll_sftp(list_id) do
    {:ok, conn} = connect()
    find_file(conn, list_id)
  end

  def delete(list_id) do
    {:ok, conn} = connect()
    delete(conn, list_id)
  end

  defp upload(conn, local_dir, filename) do
    SFTPClient.upload_file(conn, local_dir <> "/" <> filename, "/" <> filename)
  end

  defp delete(conn, file_name_id_part) do
    try do
      target_file = find_file(conn, file_name_id_part)

      Logger.debug("[RND SFTP Delete] delete target file: #{target_file}")
      SFTPClient.delete_file(conn, "/" <> target_file)
    rescue
      e ->
        Logger.error(Exception.format(:error, e, __STACKTRACE__))
        {:error, e}
    end
  end

  defp find_file(conn, file_name_id_part) do
    ret =
      conn
      |> SFTPClient.list_dir!("/results")
      |> Enum.find(&String.match?(&1, ~r/^TNQueryUpload-#{file_name_id_part}.csv/))

    case ret do
      nil ->
        # At info level because I don't know if this is a warning, or if
        # we need to be more patient
        Logger.info("File not found, waiting and retrying...")
        Process.sleep(5_000)
        find_file(conn, file_name_id_part)

      not_nil ->
        not_nil
    end

    :ok
  end

  def connect do
    SFTPClient.connect(
      host: sftp_endpoint(),
      user: username(),
      private_key_path: private_key_path(),
      sftp_vsn: 3
    )
  end
end
